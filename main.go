package main

import (
	"fmt"
	"io"
	"log"
	"net/http"
)

func main() {
	BASE_URL := "https://api.openweathermap.org/data/2.5/weather"
	API_KEY := "key"

	fmt.Println("where do you want to check the weathher:")
	var city string
	fmt.Scanln(&city)

	SEARCH_URL := fmt.Sprintf("%v?appid=%s&q=%s", BASE_URL, API_KEY, city)

	response, err := http.Get(SEARCH_URL)
	if err != nil {
		log.Fatal(err)
	}

	defer response.Body.Close()

	if response.StatusCode == http.StatusOK {
		weatherBytes, _ := io.ReadAll(response.Body)
		fmt.Println(string(weatherBytes))
	}
}
