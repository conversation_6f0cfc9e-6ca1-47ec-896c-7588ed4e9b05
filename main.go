package main

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"

	"github.com/joho/godotenv"
)

// Weather struct to parse JSON response
type WeatherResponse struct {
	Name string `json:"name"`
	Main struct {
		Temp     float64 `json:"temp"`
		Humidity int     `json:"humidity"`
	} `json:"main"`
	Weather []struct {
		Main        string `json:"main"`
		Description string `json:"description"`
	} `json:"weather"`
	Wind struct {
		Speed float64 `json:"speed"`
	} `json:"wind"`
}

func main() {

	err := godotenv.Load()

	if err != nil {
		log.Fatal(err)
	}

	BASE_URL := os.Getenv("BASE_URL")
	API_KEY := os.Getenv("API_KEY")

	// Debug: Check if environment variables are loaded
	fmt.Printf("BASE_URL: %s\n", BASE_URL)
	fmt.Printf("API_KEY: %s\n", API_KEY)

	if BASE_URL == "" {
		log.Fatal("BASE_URL environment variable is not set")
	}
	if API_KEY == "" || API_KEY == "key" {
		log.Fatal("API_KEY environment variable is not set or is placeholder value")
	}

	fmt.Println("where do you want to check the weathher:")
	var city string
	fmt.Scanln(&city)

	SEARCH_URL := fmt.Sprintf("%s?appid=%s&q=%s", BASE_URL, API_KEY, city)
	fmt.Printf("Making request to: %s\n", SEARCH_URL)

	response, err := http.Get(SEARCH_URL)
	if err != nil {
		log.Fatal("Error making HTTP request:", err)
	}

	defer response.Body.Close()

	// Read response body regardless of status code
	weatherBytes, err := io.ReadAll(response.Body)
	if err != nil {
		log.Fatal("Error reading response body:", err)
	}

	fmt.Printf("HTTP Status: %d\n", response.StatusCode)
	fmt.Printf("Response: %s\n", string(weatherBytes))

	if response.StatusCode != http.StatusOK {
		fmt.Printf("API returned error status: %d\n", response.StatusCode)
	}

	json.Unmarshal(weatherBytes, &wea)
}
